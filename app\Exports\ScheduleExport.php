<?php


namespace App\Exports;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromView;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\UserPlan;


class ScheduleExport implements FromView
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function view(): View
    {
        $location = Auth::user()->location_id;
        $date  = UserPlan::find(2)?->date;

        $rotationType = $this->request->has('Rotation_1') ? 'Rotation1' : ($this->request->has('Rotation_2') ? 'Rotation2' : null);

        $employeesQuery = DB::table('users')->where('location_id', $location);

        if ($rotationType === 'Rotation1') {
            $employeesQuery->whereIn('rotation', ['Rotation1', 'Regular']);
        } elseif ($rotationType === 'Rotation2') {
            $employeesQuery->whereIn('rotation', ['Rotation2', 'Regular']);
        } else {
            $employeesQuery->whereIn('rotation', ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Rotation5', 'Regular']);
        }

        $employees = $employeesQuery->orderBy('rotation')->orderBy('rotation')->limit(30)->get();

        $scheduleQuery = DB::table('sched');
        if ($date) {
            $scheduleQuery->where('date', 'LIKE', "{$date}%");
        }
        $schedule = $scheduleQuery->get();

        return view('schedule.exportechdule', [
            'employees' => $employees,
            'schedule' => $schedule,
        ]);
    }
}
