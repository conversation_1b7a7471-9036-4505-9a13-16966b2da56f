
<?php

define('PREPEND_PATH', '../');
$hooks_dir = dirname(__FILE__);
include("header.php");
$deleteval   = isset($_GET['myvar']) ? $_GET['myvar'] : '';


if(!empty($deleteval)){


$us_id = sql("SELECT * FROM membership_users WHERE memberID = '$deleteval'", $eo);

$num_id   = mysqli_num_rows($us_id);

if ($num_id > 0){

while ( $rowid = mysqli_fetch_array($us_id)) {
$memberID     = $rowid['memberID'];

$email         = $rowid['email'];
$name          = $rowid['custom1'];
$phone         = $rowid['custom3'];
$sponsor       = $rowid['sponsor'];
$sponsor_rate  = $rowid['sponsor_rate'];
$alert_amount  = $rowid['alert_amount'];


}
}
}
?>

<meta name="viewport" content="width=device-width, initial-scale=1">

<style type="text/css">



.videos {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 0.5rem;
    padding: 0.5rem;
    border: 1px dashed blue;
}
.video {

    min-width: 50%;
}
.video:nth-child(4n),
.video:nth-child(5n),
.video:nth-child(6n) {
    min-height: 3rem;
    border: 1px dashed orange;
}

@media screen and (max-width: 900px) {

.column {width: 100%;}

}

* {
  box-sizing: border-box;
}

/* Create three equal columns that floats next to each other */
.column {
  float: left;
  width: 33.33%;
  padding: 10px;
 height: 300px
}

/* Clear floats after the columns */
.row11:after {
  content: "";
  display: table;
  clear: both;

}
@media screen and (max-width: 900px) {

.column {width: 100%;}

}



      @media  screen and (max-width: 480px) {


.column {width: 100%;

        }
</style>
                    <br><br><br>
                   
 




    <?php

if(isset($_POST['submit'])){

$memberID = is_allowed_username(Request::val('newUsername'));
$email    = isEmail(Request::val('email'));
$password = Request::val('password');
$confirmPassword = Request::val('confirmPassword');
$name        = $_POST['name'];
$phone       = $_POST['phone'];
$sponsor       = $_POST['sponsor'];
$sponsor_rate       = $_POST['sponsor_rate'];
$alert_amount       = $_POST['alert_amount'];



$group_id  = 73;

$newstring  = substr($fulldate, -3);

$voucher = $memberID.$newstring;

$us_id1 = sql("SELECT * FROM membership_users WHERE memberID = '$memberID'", $eo);

$num_id1   = mysqli_num_rows($us_id1);

if ($num_id1 > 0){



sql("update membership_users set sponsor='$sponsor',sponsor_rate='$sponsor_rate',alert_amount='$alert_amount , isBanned='0', isApproved='1' where `memberID`='$memberID'", $eo);


}else{

         sql("INSERT INTO `membership_users` set memberID='{$memberID}', passMD5='" . password_hash($password, PASSWORD_DEFAULT) . "', email='{$email}',custom1='{$name}',custom2='{$voucher}',custom3='{$phone}', signupDate='" . @date('Y-m-d') . "', groupID='{$group_id}', sponsor='{$sponsor}', sponsor_rate='{$sponsor_rate}', alert_amount='{$alert_amount}', isBanned='0', isApproved='1', comments='member signed up through the registration form.'", $eo);

}
         sql("INSERT INTO `wallet` set date='" . @date('Y-m-d') . "', transaction='Recive Amount', balance_after='0', points='0', alert_amount='{$alert_amount}', username='{$memberID}'", $eo);


if(!empty($_POST['checkbox1'])){

for ($i1 = 0; $i1 < count($_POST['checkbox1']); $i1++) {



          $product_name = sqlValue("SELECT name FROM `products` WHERE id='".$_POST['checkbox1'][$i1]."'");

         sql("INSERT INTO `user_comission` set date='" . @date('Y-m-d') . "', username='{$memberID}', voucher='{$voucher}', product_id='".$_POST['checkbox1'][$i1]."', product_name='".$product_name."', discount='".$_POST['discount'][$i1]."', commission='".$_POST['commission'][$i1]."', com_type='".$_POST['type'][$i1]."'", $eo);


        }

}



$viewreply = '<div class="col-sm-12 col-md-6"><h6 style="color: green;">You request submitted successfully  </h6></div>';


}
    
    ?>

<style>

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 120px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */

  z-index: 999;

}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}



.modal-header33 {
  padding: 2px 16px;
  background-color: #a6a6a6;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #a6a6a6;
  color: white;
}

td,th{text-align: center;}


table tr {
  border: 1px solid #ddd;
  padding: .35em;
}

table th,
table td {
  padding: .625em;
  text-align: center;
}


@media screen and (max-width: 600px) {
  table {
    border: 0;
  }

  table caption {
    font-size: 1.3em;
  }
  
  table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  
  table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: .625em;
  }
  
  table td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: .8em;
    text-align: right;
        min-height: 50px;

  }
  
  table td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    float: left;
    font-weight: bold;
  }
  
  table td:last-child {
    border-bottom: 0;
  }

.modal-content {

  width: 80%;

}

}

</style>





 <main class="bg-white-300 flex-1 p-3 overflow-hidden">
<div class="flex flex-col">

<div class="row11" >



      <!-- partial -->
      <div class="main-panel">        
        <div class="content-wrapper">
          <div class="row">

 
            <div class="col-12 grid-margin">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title">Add New User</h4>
                                     <h4 class="card-title"><?php echo $viewreply;?></h4>

                  <form action="" id="form1" method="post"  role="form1" enctype='multipart/form-data'> 
                    <p class="card-description">
                    </p>

                    <div class="row">
                    <div class="col-md-4">
                     <div class="form-group">
                
            <div class="form-group">
              <label for="username" class="control-label"><?php echo $Translation['username']; ?></label>
              <input class="form-control input-lg" type="text" required="" placeholder="<?php echo $Translation['username']; ?>" id="username"  name="newUsername" value = "<?php echo $deleteval;?>">
              <span id="usernameAvailable" class="help-block hidden pull-left"><i class="glyphicon glyphicon-ok"></i> <?php echo str_ireplace(["'", '"', '<memberid>'], '', $Translation['user available']); ?></span>
              <span id="usernameNotAvailable" class="help-block hidden pull-left"><i class="glyphicon glyphicon-remove"></i> <?php echo str_ireplace(["'", '"', '<memberid>'], '', $Translation['username invalid']); ?></span>
              <div class="clearfix"></div>
            </div>
                  </div>
                  </div>
                    <div class="col-md-4">
                     <div class="form-group">
                    <label>Name</label>
                       <input type="text" class="form-control" id="name" name="name" placeholder="Full Name" value = "<?php echo $name?>">

                  </div>
                  </div>

    
                      <div class="col-md-4">
                    <div class="form-group">
                    <label >User Email</label>
                       <input type="email" class="form-control" id="email" name="email" placeholder="User Email" value = "<?php echo $email?>">

                        </div>
                      </div>

                    </div>

                    <div class="row">

                    <div class="col-md-4">
                    <div class="form-group">
                    <label >User Phone</label>
                       <input type="text" class="form-control" id="phone" name="phone" placeholder="User Phone" value = "<?php echo $phone?>">

                        </div>
                      </div>


                      <div class="col-md-4">
                    <div class="form-group">
                    <label >Sponsor</label>

                  <select  class="form-control" id="sponsor"  name="sponsor" style="width: 100% !important;">  

                        <option value="<?php echo $sponsor; ?>"><?php if(empty($sponsor)){$sponsor = 'Select Sponsor'; echo $sponsor; }else{ echo $sponsor;} ?></option>
                            <?php
          
                            $matquery2 = sql("SELECT * FROM `membership_users` WHERE groupID = '73'", $eo);

                              while ($row2 = $matquery2->fetch_assoc()) {

                                  unset( $name2);

                                  $name2 = $row2['memberID'];?>

                              <option value="<?php echo $name2; ?>" > <?php echo $name2; ?> </option>
                                  <?php }?>

                      </select> 
                        </div>
                      </div>


                      <div class="col-md-4">
                    <div class="form-group">
                    <label >Sponsor rate</label>
                       <input type="text" class="form-control" id="sponsor_rate" name="sponsor_rate" placeholder="Sponsor rate" value = "<?php echo $sponsor_rate?>">

                        </div>
                      </div>


                    </div>

                    <div class="row">

                      <div class="col-md-4">
                    <div class="form-group">
                    <label >Alert Amount</label>
                       <input type="text" class="form-control" id="alert_amount" name="alert_amount" placeholder="Alert Amount" value = "<?php echo $alert_amount?>">

                        </div>
                      </div>



<?php if(!empty($deleteval)){}else{?>

                    <div class="col-md-4">
                    <div class="form-group">
                    <label >User Password</label>
                  <input class="form-control" type="password" autocomplete="new-password" required="" placeholder="<?php echo $Translation['password']; ?>" id="password" name="password">

                        </div>
                      </div>

                    <div class="col-md-4">
                    <div class="form-group">
                    <label >Confirm Password </label>
                  <input class="form-control" type="password" autocomplete="new-password" required="" placeholder="<?php echo $Translation['confirm password']; ?>" id="confirmPassword" name="confirmPassword">

                        </div>
                      </div>
                      </div>

<?php }?>

      <br><br><br>
      

                    <div class="row">
                    <div class="col-md-4"  id="alldata1" style="display: none;">
                    <div class="form-group">
                    <label >Discount</label>
                  <input class="form-control" type="text"  id="alldisc" name="alldisc">

                        </div>
                      </div>

                    <div class="col-md-4"  id="alldata2" style="display: none;">
                    <div class="form-group">
                    <label >Commission</label>
                  <input class="form-control" type="text"  id="allcomm" name="allcomm">

                        </div>
                      </div>

                    <div class="col-md-4"  id="alldata3" style="display: none;">
                    <div class="form-group">
                    <label >Type</label>
                        <select class="form-control" name="alltype" id="alltype" onchange="myfunall();">
                           <option value="">Select Type</option>
                           <option value="fixed">Fixed</option>
                           <option value="percentage">Persentage</option>
               
                        </select> 
                        </div>
                      </div>

                      </div>



                <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                    <th scope="col">#<input type="checkbox" name="select-all" id="select-all" /></th>
                          <th scope="col">Product</th>
                          <th scope="col">Price</th>
                          <th scope="col">Discount</th>
                          <th scope="col">Commission</th>
                          <th scope="col">Type</th>

       
                        </tr>
                      </thead>
                      <tbody>



<?php 

$us_id1 = sql("SELECT * FROM products WHERE woocommerce_disable_sync = 0", $eo);

$num_id1   = mysqli_num_rows($us_id1);

if ($num_id1 > 0){
$snr = 0;

while ( $rowid = mysqli_fetch_array($us_id1)) {
$id            = $rowid['id'];
$name         = $rowid['name'];

 $pro_price = sqlValue("SELECT sell_price_inc_tax FROM `variations` WHERE product_id='$id'");


echo'


                        <tr>
                          <td scope="row" data-label="NU"><input class="itemRow" id="procheck" name="checkbox1[]" type="checkbox" value="'.$id.'"></td>
                          <td scope="row" data-label="Product"><a '.$viewag.'>'.$name.'</a></td>
                          <td scope="row" data-label="Price"><a '.$pro_price.'>'.$pro_price.'</a></td>
                          <td scope="row" data-label="Discount"><input type="text" name="discount[]" id="discount'.$snr.'" class="form-control" placeholder="Discount"></td>
                          <td scope="row" data-label="Commission"><input type="text" name="commission[]" id="commission'.$snr.'" class="form-control" placeholder="Commission"></td>
                          <td scope="row" data-label="Type">
                             
                             <select class="form-select" name="type[]" id="type'.$snr.'">
                                             <option value="fixed">Fixed</option>
                                             <option value="percentage">Persentage</option>
               
                             </select> 


                          </td>
                        </tr>



';

$snr = $snr +1;
}}


?>


                        
                      </tbody>
                    </table>
                  </div>





     

                  <button type="submit" style="display: none;" id="PostID" class="btn btn-success" name="typechange"></button>


                   <br><br>


                    <button type="submit" class="btn btn-primary mr-2" style="background-color: #B8860B; color: white; " name="submit">Submit</button>
                    <button class="btn btn-light">Cancel</button>
                  </div>
                  </form>


                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

 <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

  <script>

$('#select-all').click(function(event) { 

var alld1 = document.getElementById("alldata1");
var alld2 = document.getElementById("alldata2");
var alld3 = document.getElementById("alldata3");



    if(this.checked) {
        // Iterate each checkbox
        $(':checkbox').each(function() {
            this.checked = true; 

            alld1.style.display = "block";
            alld2.style.display = "block";
            alld3.style.display = "block";
                       
        });
    } else {
        $(':checkbox').each(function() {
            this.checked = false;   

            alld1.style.display = "none";
            alld2.style.display = "none";
            alld3.style.display = "none";


        });
    }
}); 

function myfunall(){

alldis3 = document.getElementById("alldisc").value;
allcom3 = document.getElementById("allcomm").value;
alltyp3 = document.getElementById("alltype").value;



  for(var i=0, n=100;i<n;i++) {

    idscomm = 'commission'+[i];
    idsdisc = 'discount'+[i];
    idstype = 'type'+[i];

document.getElementById(idscomm).value = allcom3;
document.getElementById(idsdisc).value = alldis3;
document.getElementById(idstype).value = alltyp3;
  }



}



    $j(function() {
      $j('#group_name').focus();

      $j('#usernameAvailable, #usernameNotAvailable').click(function() { $j('#username').focus(); });
      $j('#username').on('keyup blur', checkUser);

      /* password strength feedback */
      $j('#password').on('keyup blur', function() {
        var ps = passwordStrength($j('#password').val(), $j('#username').val());

        if(ps == 'strong') {
          $j('#password').parents('.form-group').removeClass('has-error has-warning').addClass('has-success');
          $j('#password').attr('title', '<?php echo html_attr($Translation['Password strength: strong']); ?>');
        } else if(ps == 'good') {
          $j('#password').parents('.form-group').removeClass('has-success has-error').addClass('has-warning');
          $j('#password').attr('title', '<?php echo html_attr($Translation['Password strength: good']); ?>');
        } else {
          $j('#password').parents('.form-group').removeClass('has-success has-warning').addClass('has-error');
          $j('#password').attr('title', '<?php echo html_attr($Translation['Password strength: weak']); ?>');
        }
      });

      /* inline feedback of confirm password */
      $j('#confirmPassword').on('keyup blur', function() {
        if($j('#confirmPassword').val() != $j('#password').val() || !$j('#confirmPassword').val().length) {
          $j('#confirmPassword').parents('.form-group').removeClass('has-success').addClass('has-error');
        } else {
          $j('#confirmPassword').parents('.form-group').removeClass('has-error').addClass('has-success');
        }
      });

      /* inline feedback of email */
      $j('#email').on('change', function() {
        if(validateEmail($j('#email').val())) {
          $j('#email').parents('.form-group').removeClass('has-error').addClass('has-success');
        } else {
          $j('#email').parents('.form-group').removeClass('has-success').addClass('has-error');
        }
      });

      /* validate form before submitting */
      $j('#submit').click(function(e) { if(!jsValidateSignup()) e.preventDefault(); })
    });

    var uaro; // user availability request object
    function checkUser() {
      // abort previous request, if any
      if(uaro != undefined) uaro.abort();

      reset_username_status();

      uaro = $j.ajax({
          url: '../checkMemberID.php',
          type: 'GET',
          data: { 'memberID': $j('#username').val() },
          success: function(resp) {
            if(resp.indexOf('username-available') > -1) {
              reset_username_status('success');
            } else {
              reset_username_status('error');
            }
          }
      });
    }

    function reset_username_status(status) {
      $j('#usernameNotAvailable, #usernameAvailable')
        .addClass('hidden')
        .parents('.form-group')
        .removeClass('has-error has-success');

      if(status == undefined) return;
      if(status == 'success') {
        $j('#usernameAvailable')
          .removeClass('hidden')
          .parents('.form-group')
          .addClass('has-success');
      }
      if(status == 'error') {
        $j('#usernameNotAvailable')
          .removeClass('hidden')
          .parents('.form-group')
          .addClass('has-error');
      }
    }

    /* validate data before submitting */
    function jsValidateSignup() {
      var p1 = $j('#password').val();
      var p2 = $j('#confirmPassword').val();
      var email = $j('#email').val();

      /* user exists? */
      if(!$j('#username').parents('.form-group').hasClass('has-success')) {
        modal_window({ message: '<div class="alert alert-danger"><?php echo html_attr($Translation['username invalid']); ?></div>', title: "<?php echo html_attr($Translation['error:']); ?>", close: function() { $j('#username').focus(); } });
        return false;
      }

      /* passwords not matching? */
      if(p1 != p2) {
        modal_window({ message: '<div class="alert alert-danger"><?php echo html_attr($Translation['password no match']); ?></div>', title: "<?php echo html_attr($Translation['error:']); ?>", close: function() { $j('#confirmPassword').focus(); } });
        return false;
      }

      if(!validateEmail(email)) {
        modal_window({ message: '<div class="alert alert-danger"><?php echo html_attr($Translation['email invalid']); ?></div>', title: "<?php echo html_attr($Translation['error:']); ?>", close: function() { $j('#email').focus(); } });
        return false;
      }

      return true;
    }
  </script>





</div></div></div></div></div>

    <script src="js/jquery.min.js"></script>
    <script src="js/popper.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script></body>

</html>


