<?php

$hooks_dir = dirname(__FILE__);
include("$hooks_dir/../defaultLang.php");
include("$hooks_dir/../language.php");
include("$hooks_dir/../lib.php");
   


$mi = getMemberInfo();
$datos = $mi['username'];
$group = $mi['groupID'];

       
    if($datos == 'guest' ){
        echo error_message($Translation['tableAccessDenied'], false);
        echo '<script>window.open("../index.php?signOut=1","_self");</script>';
        exit;
    }

// $datos = 'beg_c1'; 

        $app_date      =  date('Y-m-d');
        $fulldate      =  time();

$us_profile = sql("SELECT * FROM `membership_users` WHERE `memberID` = '$datos'", $eo);

    $num_prof   = mysqli_num_rows($us_profile);

if ($num_prof > 0){

    $prprof     = mysqli_fetch_array($us_profile);

    $profile_pic   = $prprof['profile_pic'];
    $subdate       = $prprof['signupDate'];
    $category      = $prprof['custom4'];
    $voucher      = $prprof['custom2'];

if(empty($profile_pic)){$profile_pic = 'nopic.png';}


}




 $Dateformatf = date('Y-m-d');
 $Day = date('l', strtotime($Dateformatf));

$query3 =sql("SELECT date,day,user1,COUNT(*) FROM notification  where user1='$datos' AND status != 'Closed' AND day IS NULL", $eo);;
$row3 = mysqli_fetch_array($query3);
$dat = $row3["COUNT(*)"];

$query4 =sql("SELECT date,day,user1,COUNT(*) FROM notification  where user1='$datos' AND status != 'Closed' AND day='$Day'", $eo);;
$row4 = mysqli_fetch_array($query4);
$dat1 = $row4["COUNT(*)"];


if($dat1 == 0 && $dat == 0){

$stylenot = 'style="font-size:15px;color:  #5db3e1"';

}else{

$stylenot = 'style="font-size:15px; color: red"';

}

    $result6 = sql("SELECT * FROM `notification` WHERE user1='$datos' AND status = 'open' AND subject <> 'Target Report' AND stat IS NULL", $eo);
    $num_rows2 = mysqli_num_rows($result6);
    if ($num_rows2 > 0){


    ?>
<script>
$j(function(){

alert("You Received New Notification");

})
</script>

    <?php
        sql("UPDATE notification SET stat='D' WHERE stat IS NULL AND user1='$datos' AND status = 'open'", $eo);  


}


 $products_num = sqlValue("SELECT COUNT(*) FROM `user_comission` WHERE username = '$datos'");

 $orders_num = sqlValue("SELECT COUNT(*) FROM `commission_plane` WHERE username = '$datos'");

 $balance = sqlValue("SELECT SUM(balance_after) FROM `wallet` WHERE  username = '$datos' AND transaction = 'New Invoice'");


 $comission = sqlValue("SELECT SUM(points) FROM `wallet` WHERE  username = '$datos'");




?>




<?php
    /*
        Classes of first and other blocks
        ---------------------------------
        For possible classes, refer to the Bootstrap grid columns, panels and buttons documentation:
            Grid columns: http://getbootstrap.com/css/#grid
            Panels: http://getbootstrap.com/components/#panels
            Buttons: http://getbootstrap.com/css/#buttons
    */
    $block_classes = array(
        'first' => array(
            'grid_column' => 'col-lg-12',
            'panel' => 'panel-warning',
            'link' => 'btn-warning'
        ),
        'other' => array(
            'grid_column' => 'col-lg-6',
            'panel' => 'panel-info',
            'link' => 'btn-info'
        )
    );
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="keywords" content="tailwind,tailwindcss,tailwind css,css,starter template,free template,admin templates, admin template, admin dashboard, free tailwind templates, tailwind example">
    <!-- Css -->
<!--     <link rel="stylesheet" href="cerggg.css">
 -->    
<link rel="stylesheet" href="./dist/styles.css">
<link rel="stylesheet" href="./dist/all.css">
<link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,600,600i,700,700i" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <title>Dashboard</title>
</head>


  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700,800,900" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/style33.css">
    <link rel="icon" href="../logo.png" type="image/gif" sizes="10x10">
   
  </head>
<body >

      <style type="text/css">


.goog-te-gadget img{
    display:none !important;
}
body > .skiptranslate {
    display: none;
}
body {
    top: 0px !important;
}



</style>
     <!--Container -->
<div class="mx-auto bg-grey-400">
    <!--Screen-->
    <div class="min-h-screen flex flex-col">
        <!--Header Section Starts Here-->
        <header class="bg-nav" style="background-color:#1a1a1a">
            <div class="flex justify-between">
                <div class="p-1 mx-3 inline-flex items-center">


            <i > <button type="button" style="background-color: #5db3e1; color: white; " id="sidebarCollapse" class="btn" >
              <i class="fa fa-bars" ></i>
              <span class="sr-only">Toggle Menu</span>
            </button></i>
  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

             

                <img  class="dis22"  src="../images/1644072101_autostore.png" style="width: 150px;">

                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;


        
                      </div>


                         <div class="flex flex-row items-center mobilw">

                                <a href="../membership_profile.php">

                <img onclick="profileToggle()"  style="width: 70px;height: 70px; border-radius: 20%;   " src="../images/<?php echo $profile_pic;?>" alt="">

                             </a>



                    <a href="../membership_profile.php" onclick="profileToggle()" class="text-white p-2 no-underline hidden md:block lg:block"><?php echo $datos;?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
                </div>
  
            </div>


        </header>
        <!--/Header-->

        <div class="flex flex-1" >
            <!--Sidebar-->
            <aside id="sidebar" class="bg-side-nav   md:block lg:block"  style="background-color:#1a1a1a">

                <ul class="list-reset flex flex-col">
        
        <div class="wrapper d-flex align-items-stretch">
  
                <div class="p-4">
   


  <li class="active">
<div id="google_translate_element"></div>

<script type="text/javascript">
function googleTranslateElementInit() {
  new google.translate.TranslateElement({pageLanguage: 'en'}, 'google_translate_element');
}
</script>

<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>      


   </li> 

    
                


                         <ul class="list-unstyled components mb-5">




              <li class="active">
                <a style="font-size: 15px; "  href="../index.html"><span class="fa fa-home mr-3"></span> Home</a>
              </li>
       

<?php if($group == 2){?>
       <li class="active">
                <a style="font-size: 15px; color: #5db3e1"  href="add_new_user.php"><span class="fa fa-sticky-note mr-3"></span>  Add New User</a>
              </li>
       <li class="active">
                <a style="font-size: 15px;   color: #5db3e1"  href="pay_user.php"><span class="fa fa-sticky-note mr-3"></span>  Add New Payment</a>
              </li>



       <li class="active">
                <a <?php echo $stylenot ;?> href="notification.php"><span class="fa fa-sticky-note mr-3"></span>  Notifications</a>
              </li>





<?php }?>


                            <li>
        <a style="font-size: 18px;  font-weight: bold;" ><span class="fa fa-user mr-3"></span> Tables</a>
              </li><br>


<style type="text/css">
    
.btn-group:hover {
            color:#B8860B;
            cursor: pointer;
        }
.btn-group {
            font-size: 14px;
            color: white;
            font-weight: bold;
            font-family: Arial, Helvetica, sans-serif;
            width: 200px;
            padding: 3px 0px;
        }

          .btn-primary:hover {
    color: #fff;
    background-color: black;
    border-color: #0062cc; }

.dis22{width: 100px;}

@media screen and (max-width: 900px) {
   .dis22 {
     font-size: 20px;
     width: 200px;

   }
      .dis33 {
     font-size: 20px;

   }
.justify-between{width: 94%;}

}
@media screen and (max-width: 600px) {
   .dis22 {
     font-size: 20px;
          width: 170px;

   }
      .dis33 {
     font-size: 20px;

   }
.justify-between{width: 94%;}

}

</style>

<?php
    /* accessible tables */
    $arrTables = get_tables_info();
    if(is_array($arrTables) && count($arrTables)){
        /* how many table groups do we have? */
        $groups = get_table_groups();
        $multiple_groups = (count($groups) > 1 ? true : false);

        /* construct $tg: table list grouped by table group */
        $tg = array();
        if(count($groups)){
            foreach($groups as $grp => $tables){
                foreach($tables as $tn){
                    $tg[$tn] = $grp;
                }
            }
        }

        $i = 0; $current_group = '';
        foreach($tg as $tn => $tgroup){
            $tc = $arrTables[$tn];
            /* is the current table filter-first? */
            $tChkFF = array_search($tn, array());
            /* hide current table in homepage? */
            $tChkHL = array_search($tn, array());
            /* allow homepage 'add new' for current table? */
        $tChkAHAN = array_search($tn, array('members_dashboard','boards_details','boards','susbscribe_request','board_structure','referrers','courses','live_course','notification','packages','vouchers','withdraw_history','app_date'));

            /* homepageShowCount for current table? */
            $count_badge = '';
            if($tc['homepageShowCount']){
                $sql_from = get_sql_from($tn);
                $count_records = ($sql_from ? sqlValue("select count(1) from " . $sql_from) : 0);
                $count_badge = '<span class="badge hspacer-lg text-bold">' . number_format($count_records) . '</span>';
            }

            $t_perm = getTablePermissions($tn);
            $can_insert = $t_perm['insert'];

            $searchFirst = (($tChkFF !== false && $tChkFF !== null) ? '?Filter_x=1' : '');
            ?>
                <?php if(!$i && !$multiple_groups){ /* no grouping, begin row */ ?>

                    <div >
                <?php } ?>
                <?php if($multiple_groups && $current_group != $tgroup){ /* grouping, begin group & row */ ?>
                    <?php if($current_group != ''){ /* not first group, so we should first end previous group */ ?>

                            </div><!-- /.table_links -->
                            <div >

                                <?php
                                    /* custom home links for current group, as defined in "hooks/links-home.php" */
                                    echo get_home_links($homeLinks, $block_classes['other'], $current_group);
                                ?>
                            </div>
                        </div><!-- /.collapse -->
                    <?php } ?>
                    <?php $current_group = $tgroup; ?>

                    <a  data-toggle="collapse" href="#group-<?php echo md5($tgroup); ?>"><?php echo $tgroup; ?> <i class="glyphicon glyphicon-chevron-right"></i></a>
                    <div id="group-<?php echo md5($tgroup); ?>">
                        <div >
                <?php } ?>

                    <?php if($tChkHL === false || $tChkHL === null){ /* if table is not set as hidden in homepage */ ?>
                        <div id="<?php echo $tn; ?>-tile" >
                            <div>

                                <div >
                                    <?php if($can_insert && $tChkAHAN !== false && $tChkAHAN !== null){ ?>


                              
                                             <div style="font-size: 12px;">
                                            <li>

                                           <a class="btn-group" title="<?php echo preg_replace("/&amp;(#[0-9]+|[a-z]+);/i", "&$1;", html_attr(strip_tags($tc['Description']))); ?>" href="..\<?php echo $tn; ?>_view.php<?php echo $searchFirst; ?>">
                                           <span class="fa fa-sticky-note mr-3"></span><?php echo $tc['Caption']; ?>&nbsp;&nbsp;<?php echo $count_badge; ?> </a>
                                               </li>
                                        </div>
                                    <?php }else{ ?>
                                          <div style="font-size: 12px;">
                                            <li>
                                        <a class="btn-group" title="<?php echo preg_replace("/&amp;(#[0-9]+|[a-z]+);/i", "&$1;", html_attr(strip_tags($tc['Description']))); ?>"  href="..\<?php echo $tn; ?>_view.php<?php echo $searchFirst; ?>">

                                            <span class="fa fa-sticky-note mr-3"></span><?php echo $tc['Caption']; ?><?php echo $count_badge; ?></a>
                                          </li>
                                        </div>
                                    <?php } ?>

                                  </div>
                            </div>
                        </div>
                    <?php } ?>
                <?php if($i == (count($arrTables) - 1) && !$multiple_groups){ /* no grouping, end row */ ?>

                    </div> <!-- /.table_links -->

                    <div  id="custom_links">
                        <?php
                            /* custom home links, as defined in "hooks/links-home.php" */
                            echo get_home_links($homeLinks, $block_classes['other'], '*');
                        ?>
                    </div>

                <?php } ?>
                <?php if($i == (count($arrTables) - 1) && $multiple_groups){ /* grouping, end last group & row */ ?>

                            </div> <!-- /.table_links -->
                            <div  id="custom_links">
                                <?php
                                    /* custom home links for last table group, as defined in "hooks/links-home.php" */
                                    echo get_home_links($homeLinks, $block_classes['other'], $tgroup);

                                    /* custom home links having no table groups, as defined in "hooks/links-home.php" */
                                    echo get_home_links($homeLinks, $block_classes['other']);
                                ?>
                            </div>
                        </div><!-- /.collapse -->
                <?php } ?>
            <?php
            $i++;
        }
    }else{
      
    }
?>







<script type="text/javascript">
  
function showfinction() {
 var txy = document.getElementById("corid");


      if (txy.style.display === "none") {
          txy.style.display = "block";
      }else{
txy.style.display = "none";

      }
      }
</script>

     

              <li class="active">
                <a class="font-sans font-hairline hover:font-normal text-sm text-nav-item no-underline" style="font-size: 18px;  font-weight: bold;"  href="../membership_profile.php">
                              <span class="fa fa-user mr-3"></span>
                           My Profile
                        </a> 
              </li>


               <li class="active">
                <a class="font-sans font-hairline hover:font-normal text-sm text-nav-item no-underline" style="font-size: 18px;  font-weight: bold;color: #8B0000;"  href="../index.php?signOut=1">
                              <span class="fa fa-user mr-3"></span>
                            Logout
                        </a> 
              </li>
     




            </ul>


          </div>
           </nav>
              </div>
 
                </ul>
            </aside>
            <!--/Sidebar-->


