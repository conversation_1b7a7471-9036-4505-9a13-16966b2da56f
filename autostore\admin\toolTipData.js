var FiltersEnabled = 0; // if your not going to use transitions or filters in any of the tips set this to 0
var spacer="&nbsp; &nbsp; &nbsp; ";

// email notifications to admin
notifyAdminNewMembers0Tip=["", spacer+"No email notifications to admin."];
notifyAdminNewMembers1Tip=["", spacer+"Notify admin only when a new member is waiting for approval."];
notifyAdminNewMembers2Tip=["", spacer+"Notify admin for all new sign-ups."];

// visitorSignup
visitorSignup0Tip=["", spacer+"If this option is selected, visitors will not be able to join this group unless the admin manually moves them to this group from the admin area."];
visitorSignup1Tip=["", spacer+"If this option is selected, visitors can join this group but will not be able to sign in unless the admin approves them from the admin area."];
visitorSignup2Tip=["", spacer+"If this option is selected, visitors can join this group and will be able to sign in instantly with no need for admin approval."];

// orders table
orders_addTip=["",spacer+"This option allows all members of the group to add records to the 'Orders' table. A member who adds a record to the table becomes the 'owner' of that record."];

orders_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Orders' table."];
orders_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Orders' table."];
orders_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Orders' table."];
orders_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Orders' table."];

orders_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Orders' table."];
orders_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Orders' table."];
orders_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Orders' table."];
orders_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Orders' table, regardless of their owner."];

orders_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Orders' table."];
orders_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Orders' table."];
orders_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Orders' table."];
orders_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Orders' table."];

// customers table
customers_addTip=["",spacer+"This option allows all members of the group to add records to the 'Customers' table. A member who adds a record to the table becomes the 'owner' of that record."];

customers_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Customers' table."];
customers_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Customers' table."];
customers_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Customers' table."];
customers_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Customers' table."];

customers_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Customers' table."];
customers_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Customers' table."];
customers_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Customers' table."];
customers_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Customers' table, regardless of their owner."];

customers_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Customers' table."];
customers_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Customers' table."];
customers_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Customers' table."];
customers_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Customers' table."];

// wallet table
wallet_addTip=["",spacer+"This option allows all members of the group to add records to the 'Wallet' table. A member who adds a record to the table becomes the 'owner' of that record."];

wallet_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Wallet' table."];
wallet_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Wallet' table."];
wallet_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Wallet' table."];
wallet_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Wallet' table."];

wallet_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Wallet' table."];
wallet_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Wallet' table."];
wallet_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Wallet' table."];
wallet_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Wallet' table, regardless of their owner."];

wallet_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Wallet' table."];
wallet_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Wallet' table."];
wallet_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Wallet' table."];
wallet_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Wallet' table."];

// commission_plane table
commission_plane_addTip=["",spacer+"This option allows all members of the group to add records to the 'Commission plane' table. A member who adds a record to the table becomes the 'owner' of that record."];

commission_plane_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Commission plane' table."];
commission_plane_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Commission plane' table."];
commission_plane_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Commission plane' table."];
commission_plane_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Commission plane' table."];

commission_plane_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Commission plane' table."];
commission_plane_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Commission plane' table."];
commission_plane_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Commission plane' table."];
commission_plane_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Commission plane' table, regardless of their owner."];

commission_plane_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Commission plane' table."];
commission_plane_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Commission plane' table."];
commission_plane_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Commission plane' table."];
commission_plane_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Commission plane' table."];

// notification table
notification_addTip=["",spacer+"This option allows all members of the group to add records to the 'Notification' table. A member who adds a record to the table becomes the 'owner' of that record."];

notification_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Notification' table."];
notification_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Notification' table."];
notification_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Notification' table."];
notification_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Notification' table."];

notification_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Notification' table."];
notification_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Notification' table."];
notification_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Notification' table."];
notification_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Notification' table, regardless of their owner."];

notification_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Notification' table."];
notification_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Notification' table."];
notification_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Notification' table."];
notification_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Notification' table."];

// partners table
partners_addTip=["",spacer+"This option allows all members of the group to add records to the 'Partners' table. A member who adds a record to the table becomes the 'owner' of that record."];

partners_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Partners' table."];
partners_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Partners' table."];
partners_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Partners' table."];
partners_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Partners' table."];

partners_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Partners' table."];
partners_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Partners' table."];
partners_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Partners' table."];
partners_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Partners' table, regardless of their owner."];

partners_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Partners' table."];
partners_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Partners' table."];
partners_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Partners' table."];
partners_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Partners' table."];

// payment table
payment_addTip=["",spacer+"This option allows all members of the group to add records to the 'Payment' table. A member who adds a record to the table becomes the 'owner' of that record."];

payment_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Payment' table."];
payment_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Payment' table."];
payment_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Payment' table."];
payment_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Payment' table."];

payment_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Payment' table."];
payment_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Payment' table."];
payment_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Payment' table."];
payment_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Payment' table, regardless of their owner."];

payment_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Payment' table."];
payment_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Payment' table."];
payment_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Payment' table."];
payment_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Payment' table."];

// products table
products_addTip=["",spacer+"This option allows all members of the group to add records to the 'Products' table. A member who adds a record to the table becomes the 'owner' of that record."];

products_view0Tip=["",spacer+"This option prohibits all members of the group from viewing any record in the 'Products' table."];
products_view1Tip=["",spacer+"This option allows each member of the group to view only his own records in the 'Products' table."];
products_view2Tip=["",spacer+"This option allows each member of the group to view any record owned by any member of the group in the 'Products' table."];
products_view3Tip=["",spacer+"This option allows each member of the group to view all records in the 'Products' table."];

products_edit0Tip=["",spacer+"This option prohibits all members of the group from modifying any record in the 'Products' table."];
products_edit1Tip=["",spacer+"This option allows each member of the group to edit only his own records in the 'Products' table."];
products_edit2Tip=["",spacer+"This option allows each member of the group to edit any record owned by any member of the group in the 'Products' table."];
products_edit3Tip=["",spacer+"This option allows each member of the group to edit any records in the 'Products' table, regardless of their owner."];

products_delete0Tip=["",spacer+"This option prohibits all members of the group from deleting any record in the 'Products' table."];
products_delete1Tip=["",spacer+"This option allows each member of the group to delete only his own records in the 'Products' table."];
products_delete2Tip=["",spacer+"This option allows each member of the group to delete any record owned by any member of the group in the 'Products' table."];
products_delete3Tip=["",spacer+"This option allows each member of the group to delete any records in the 'Products' table."];

/*
	Style syntax:
	-------------
	[TitleColor,TextColor,TitleBgColor,TextBgColor,TitleBgImag,TextBgImag,TitleTextAlign,
	TextTextAlign,TitleFontFace,TextFontFace, TipPosition, StickyStyle, TitleFontSize,
	TextFontSize, Width, Height, BorderSize, PadTextArea, CoordinateX , CoordinateY,
	TransitionNumber, TransitionDuration, TransparencyLevel ,ShadowType, ShadowColor]

*/

toolTipStyle=["white","#00008B","#000099","#E6E6FA","","images/helpBg.gif","","","","\"Trebuchet MS\", sans-serif","","","","3",400,"",1,2,10,10,51,1,0,"",""];

applyCssFilter();
