/* modal box */
.modalBox {
	position: fixed;
	width: 0%;
	height: 0%;	
	display: block;
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	background: rgba($dark-theme-color,.3);

	.modalBox-body {
		width: 0%;
		height: 0%;
		transition: height .4s;
		overflow: hidden;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	&.show {
		opacity: 1;
		visibility: visible;
		top: 0;
		width: 100%;
		height: 100%;

		.modalBox-body {
			width: 70%;
			height: 450px;
			
		}
	}
}