<?php

define('PREPEND_PATH', '../');
$hooks_dir = dirname(__FILE__);
include("header.php");

?>

            <!--Main-->
            <main class="bg-white-300 flex-1 p-3 overflow-hidden">

                <div class="flex flex-col">
                    <!-- Stats Row Starts Here -->
                    <div class="flex flex-1 flex-col md:flex-row lg:flex-row mx-2">
                        <div class=" bg-success  md:w-1/4 mx-2" style="background-color: #5db3e1;border-radius: 12px;">
                            <div class="p-4 flex flex-col">
                                <a  class="no-underline text-white text-2xl fontt" style="font-size: 18px;">
                                   الرصيد
                                </a>
                                <a  class="no-underline text-white text-lg" style="font-size: 18px;">

                                    <?php
                                     if(empty($balance)){$balance = 0 ;}
                                     echo $balance . ' ' . 'EGP' ?>
                                 
                                </a>
        

                            </div>
                        </div><br>

                        <div class=" bg-success  md:w-1/4 mx-2" style="background-color: #5db3e1;border-radius: 12px;">
                            <div class="p-4 flex flex-col">
                                <a  class="no-underline text-white text-2xl fontt" style="font-size: 18px;">
                                   اجمالى العمولات  
                                </a>
                                <a class="no-underline text-white text-lg">
                                    <?php echo $comission;  ?>
                                </a>
                            </div>
                        </div><br>

                        <div class=" bg-success  md:w-1/4 mx-2" style="background-color: #5db3e1;border-radius: 12px;">
                            <div class="p-4 flex flex-col">
                                <a class="no-underline text-white text-2xl fontt" style="font-size: 18px;">
                                   عدد المنتجات
                                </a>
                                <a  class="no-underline text-white text-lg">
                                    <?php echo $products_num;  ?>
                                </a>
                            </div>
                        </div><br>

                        <div class=" bg-success  md:w-1/4 mx-2" style="background-color: #5db3e1;border-radius: 12px;">
                            <div class="p-4 flex flex-col">
                                <a  class="no-underline text-white text-2xl fontt" style="font-size: 18px;">
                                     عدد الطلبات
                                </a>
                                <a  class="no-underline text-white text-lg">
                                    <?php echo $orders_num;?>
                                </a>
                            </div>
                        </div><br>



                    </div></div>

    <style type="text/css">



        input[type=submit] {
            width: 15%;
            background-color: #4CAF50;
            color: white;
            padding: 4px 2px;
            font-size:12px;
            font-weight: bold;
            margin: 4px 0;
            border: none;
            border-radius: 2px;
            cursor: pointer;

        }


           .inputt {
            width: 20%;
            background-color: #A9A9A9;
            color: black;
            padding: 4px 2px;
            font-size:12px;
            font-weight: bold;
            margin: 4px 0;
            border: none;
            border-radius: 2px;
            text-align: center;

        }
    </style>








                        <div class="rounded overflow-hidden shadow bg-white mx-2 w-full">
                            <div class="px-6 py-2 border-b border-light-grey">

<br><br><br>




    <header  class="fh5co-cover"  role="banner" style="height: 300px;      background-size:     contain;                     
    background-repeat:   no-repeat;
    background-position: center center;  background-image:url(../images/1644071231_logo.jpg);">
  
       <div align="center">
                <div class="col-md-8 col-md-offset-2 text-center">
                    <div  style="width: 100%;">
                      <br><br><br><br><br>

                        
                    </div>
                </div>
           </div>
      
    </header>

</div></div><br><br><br>

       <div class="rounded overflow-hidden shadow bg-white mx-2 w-full">
                            <div class="px-6 py-2 border-b border-light-grey">

        <!-- partial -->
        <div class="main-panel" >
          <div class="content-wrapper">
            <div class="page-header">
              <h3 class="page-title fontt"> الجداول الرئيسيه </h3>
          
            </div>
            <div class="row">
              <div class="col-lg-6 grid-margin stretch-card">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title fontt">بيانات التسجيل</h4>
                
                    <table class="table fontt" dir="rtl">
                      <thead>
                        <tr>
                          <th>تاريخ التسجيل</th>
                                 <th><?php echo $subdate ;?></th>

                        </tr>
                      </thead>
         
                    </table>

                    <table class="table fontt" dir="rtl">
                      <thead>
                        <tr>
                          <th> نسخ رابط الاشتراك</th>
                        
                                <th style="font-size: 15px;font-weight:bold; color: red;">  

        <a href="https://autostore-eg.com/pos/order_form/index.php?user=<?php echo $datos;?>" class="font-sans font-hairline hover:font-normal text-sm text-nav-item no-underline">
                    
        <input style="display: none;" type="text" value="https://autostore-eg.com/pos/order_form/index.php?user=<?php echo $voucher;?>" id="linkad">

                        </a><button onclick="myFunctionc2()">Copy Invitation Link</button>


  </th>
                        </tr>
                        <tr>
                          <th>الارسال عن طريق الواتس</th>
                        
                                <th style="font-size: 15px;font-weight:bold; color: red;">  

       

<?php
$txt_1 = 'To Know More About Autostore You Can Use The Link Below To Contact us.'."%0A";
$txt_2 = 'https://link.gettap.co/Autostoreofficial56615'."%0D%0A"."%0D%0A";
$txt_3 = 'The Link Below Is For Creative Tint Online order'."%0D%0A";
$txt_4 = 'https://autostore-eg.com/pos/order_form/index.php?user='.$voucher.''."%0D%0A";

$txt_5 = 'استخدام الرابط للتواصل مع  اوتوستور و معرفه اماكن الفروع والسوشيل مديا'."%0A";
$txt_6 = 'https://link.gettap.co/Autostoreofficial56615'."%0D%0A"."%0D%0A";
$txt_7 = 'يمكنك استخدام الرابط التالي لخدمه شحن الفاميه الكريتف والحصول  الخصم الخاص به'."%0D%0A";
$txt_8 = 'https://autostore-eg.com/pos/order_form/index.php?user='.$voucher.''."%0D%0A";



$msg= $txt_1.$txt_2.$txt_3.$txt_4.$txt_5.$txt_6.$txt_7.$txt_8."%0A";

 $actual_link = urlencode('


https://autostore-eg.com/pos/order_form/index.php?user='.$voucher.'




');?>
<a style="padding: 10px 5px !important;color: green;" href='https://wa.me/?text=<?php echo $msg ?>'> &nbsp;Send to Whatsapp&nbsp;<i class="fa fa-whatsapp" style="font-size: 20px;"></i></a>





  </th>
<tr>

                          <th>كود الخصم</th>
                        
                                <th style="font-size: 15px;font-weight:bold; color: red;">  

       
<a style="padding: 10px 5px !important;color: green;" ><?php echo $voucher;?></a>






  </th>





                        </tr>


                      </thead>
                  

                    </table>

                  </div>
                </div>
              </div>


<style>


td,th{text-align: center;}


.tablev tr {
  border: 1px solid #ddd;
  padding: .35em;
}

.tablev th,
.tablev td {
  padding: .625em;
  text-align: center;
}


@media screen and (max-width: 600px) {
  .tablev {
    border: 0;
  }

  .tablev caption {
    font-size: 1.3em;
  }
  
  .tablev thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  
  .tablev tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: .625em;
  }
  
  .tablev td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: .8em;
    text-align: right;
        min-height: 50px;

  }
  
  .tablev td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    float: left;
    font-weight: bold;
  }
  
  .tablev td:last-child {
    border-bottom: 0;
  }



}

</style>








              <div class="col-lg-6 grid-margin stretch-card">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title fontt">قائمه المنتجات</h4>
                   
                    <table class="table table-hover fontt tablev" dir="rtl">
                      <thead>
                        <tr>
                          <th scope="col">المنتج</th>
                          <th scope="col">السعر</th>
                          <th scope="col">الخصم</th>
                          <th scope="col">العموله</th>
                          <th scope="col">النوع</th>
                        </tr>
                      </thead>
                      <tbody>



<?php

if($group == 2){


$us_id1 = sql("SELECT * FROM products WHERE   id > 0 AND woocommerce_disable_sync = 0 order by id", $eo);
$num_id1   = mysqli_num_rows($us_id1);
if ($num_id1 > 0){
while ( $rowid = mysqli_fetch_array($us_id1)) {
$product_id = $rowid['id'];

 $pro_price = sqlValue("SELECT sell_price_inc_tax FROM `variations` WHERE product_id='$product_id'");



echo '

                        <tr dir="rtl">
                          <td  scope="row" data-label="المنتج">'.$rowid['name'].'</td>
                          <td  scope="row" data-label="السعر">'. number_format($pro_price).'</td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>


';


}}

}else{


$us_id1 = sql("SELECT * FROM user_comission WHERE   username = '$datos' order by id", $eo);

$num_id1   = mysqli_num_rows($us_id1);

if ($num_id1 > 0){

while ( $rowid = mysqli_fetch_array($us_id1)) {

$product_id = $rowid['product_id'];
$discount   = $rowid['discount'];
$commission = $rowid['commission'];
$com_type = $rowid['com_type'];

 $pro_price = sqlValue("SELECT sell_price_inc_tax FROM `variations` WHERE product_id='$product_id'");


$us_id2 = sql("SELECT * FROM products WHERE  id = '$product_id' order by id", $eo);
$rowid1 = mysqli_fetch_array($us_id2);

echo '

                        <tr dir="rtl">
                          <td  scope="row" data-label="المنتج">'.$rowid1['name'].'</td>
                          <td  scope="row" data-label="السعر">'. number_format($pro_price).'</td>
                          <td  scope="row" data-label="الخصم">'. number_format($discount).'</td>
                          <td  scope="row" data-label="العموله">'. number_format($commission).'</td>
                          <td  scope="row" data-label="النوع">'.$com_type.'</td>
                        </tr>


';


}}
}

?>

                  
                         
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
     

           
            </div>
          </div>
          <!-- content-wrapper ends -->

          <!-- partial -->
        </div>
        <!-- main-panel ends -->
      </div>
</div></main></div></div></div></body></html>






















<script>
function myFunctionc2() {
  // Get the text field
  var copyText = document.getElementById("linkad");

  // Select the text field
  copyText.select();
  copyText.setSelectionRange(0, 99999); // For mobile devices

  // Copy the text inside the text field
  navigator.clipboard.writeText(copyText.value);
  
  // Alert the copied text
  alert("Copied the text: " + copyText.value);
}
</script>
<br><br><br>


              



<br><br><br><br>










                </div>
            </main>
            <!--/Main-->
        </div>


    </div>
<link href='https://fonts.googleapis.com/css?family=Cairo' rel='stylesheet'>
<style>

   .fontt{ font-family: 'Cairo';

text-align: right;

 }



</style>
</div>
<script src="main.js"></script>

    <script src="js/jquery.min.js"></script>
    <script src="js/popper.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>
</body>

</html>



