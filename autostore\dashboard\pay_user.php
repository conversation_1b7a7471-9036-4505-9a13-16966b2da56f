


<?php

define('PREPEND_PATH', '../');
$hooks_dir = dirname(__FILE__);
include("header.php");

?>

<meta name="viewport" content="width=device-width, initial-scale=1">

<style type="text/css">



.videos {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 0.5rem;
    padding: 0.5rem;
    border: 1px dashed blue;
}
.video {

    min-width: 50%;
}
.video:nth-child(4n),
.video:nth-child(5n),
.video:nth-child(6n) {
    min-height: 3rem;
    border: 1px dashed orange;
}

@media screen and (max-width: 900px) {

.column {width: 100%;}

}

* {
  box-sizing: border-box;
}

/* Create three equal columns that floats next to each other */
.column {
  float: left;
  width: 33.33%;
  padding: 10px;
 height: 300px
}

/* Clear floats after the columns */
.row11:after {
  content: "";
  display: table;
  clear: both;

}
@media screen and (max-width: 900px) {

.column {width: 100%;}

}



      @media  screen and (max-width: 480px) {


.column {width: 100%;

        }
</style>
                    <br><br><br>
                   
 




    <?php

if(isset($_POST['submit'])){

$username     = $_POST['username'];
$amount       = $_POST['amount'];


$result3 = sql("SELECT * FROM wallet WHERE username =  '$username' AND transaction = 'New Invoice'", $eo);
$row3 = mysqli_fetch_array($result3);
$balance_after = $row3['balance_after'];
$points = $row3['points'];

$newbalance = $balance_after - $amount ;


    sql("INSERT INTO wallet (date,balance_after,username,transaction)

      VALUES ('".@date('Y-m-d')."','$amount','$username','Recive Amount')", $eo);




    sql("UPDATE wallet set balance_after='$newbalance',date='".@date('Y-m-d')."' where `username`='$username' AND transaction = 'New Invoice'", $eo);


$viewreply = '<div class="col-sm-12 col-md-6"><h6 style="color: green;">You request submitted successfully  </h6></div>';


}
    
  if (isset($_POST['typechange'])){ 

   $username  = $_POST['username'];


$result3 = sql("SELECT * FROM wallet WHERE username =  '$username' AND transaction = 'New Invoice'", $eo);
$row3 = mysqli_fetch_array($result3);
$balance_after = $row3['balance_after'];
$points = $row3['points'];



}

    ?>

<style>

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 120px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */

  z-index: 999;

}

/* Modal Content */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  border: 1px solid #888;
  width: 40%;
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s
}

/* Add Animation */
@-webkit-keyframes animatetop {
  from {top:-300px; opacity:0} 
  to {top:0; opacity:1}
}

@keyframes animatetop {
  from {top:-300px; opacity:0}
  to {top:0; opacity:1}
}

/* The Close Button */
.close {
  color: white;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}



.modal-header33 {
  padding: 2px 16px;
  background-color: #a6a6a6;
  color: white;
}

.modal-body {padding: 2px 16px;}

.modal-footer {
  padding: 2px 16px;
  background-color: #a6a6a6;
  color: white;
}

td,th{text-align: center;}


table tr {
  border: 1px solid #ddd;
  padding: .35em;
}

table th,
table td {
  padding: .625em;
  text-align: center;
}


@media screen and (max-width: 600px) {
  table {
    border: 0;
  }

  table caption {
    font-size: 1.3em;
  }
  
  table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  
  table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: .625em;
  }
  
  table td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: .8em;
    text-align: right;
        min-height: 50px;

  }
  
  table td::before {
    /*
    * aria-label has no advantage, it won't be read inside a table
    content: attr(aria-label);
    */
    content: attr(data-label);
    float: left;
    font-weight: bold;
  }
  
  table td:last-child {
    border-bottom: 0;
  }

.modal-content {

  width: 80%;

}

}

</style>





 <main class="bg-white-300 flex-1 p-3 overflow-hidden">
<div class="flex flex-col">

<div class="row11" >



      <!-- partial -->
      <div class="main-panel">        
        <div class="content-wrapper">
          <div class="row">

 
            <div class="col-12 grid-margin">
              <div class="card">
                <div class="card-body">
                  <h4 class="card-title">Add New User</h4>
                                     <h4 class="card-title"><?php echo $viewreply;?></h4>

                  <form action="" id="form1" method="post"  role="form1" enctype='multipart/form-data'> 
                    <p class="card-description">
                    </p>

                    <div class="row">
                    <div class="col-md-6">
                     <div class="form-group">
                
              <label for="username" class="control-label">Username</label>

                    <select  class="form-control" id="username" required name="username" onchange="myFunction2ch();">  

                        <option value="<?php echo $username; ?>"><?php if(empty($username)){$username = 'Select username'; echo $username; }else{ echo $username;} ?></option>
                            <?php
          
                            $matquery2 = sql("SELECT * FROM `membership_users` WHERE groupID = '73' ORDER BY memberID", $eo);

                              while ($row2 = $matquery2->fetch_assoc()) {

                                  unset( $name2);

                                  $name2 = $row2['memberID'];?>

                              <option value="<?php echo $name2; ?>" > <?php echo $name2; ?> </option>
                                  <?php }?>

                      </select>  

                  </div>
                  </div>
                    <div class="col-md-6">
                     <div class="form-group">
                    <label>Amount</label>
                       <input type="text" class="form-control" id="amount" name="amount" placeholder="Amount">

                  </div>
                  </div>
                    </div>





                    <div class="row">
                    <div class="col-md-6">
                     <div class="form-group">
                
              <label for="username" class="control-label">Balance</label>

                       <input type="text" class="form-control" id="balance" readonly name="balance" placeholder="Balance" value="<?php echo $balance_after ; ?>">


                  </div>
                  </div>
                    <div class="col-md-6">
                     <div class="form-group">
                    <label>Points</label>
                       <input type="text" class="form-control" id="points" readonly name="points" placeholder="Points" value="<?php echo $points ; ?>">

                  </div>
                  </div>
                    </div>

    
      <br><br><br>
      





                  <button type="submit" style="display: none;" id="PostID" class="btn btn-success" name="typechange"></button>


                   <br><br>


                    <button type="submit" class="btn btn-primary mr-2" style="background-color: #B8860B; color: white; " name="submit">Submit</button>
                    <button class="btn btn-light">Cancel</button>
                  </div>
                  </form>


                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

 
  <script>

function myFunction2ch() {


  document.getElementById("PostID").click();

}
 
  </script>





</div></div></div></div></div>

    <script src="js/jquery.min.js"></script>
    <script src="js/popper.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script></body>

</html>


