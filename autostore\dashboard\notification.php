<?php

define('PREPEND_PATH', '../');
$hooks_dir = dirname(__FILE__);
include("header.php");





$deleteval   = isset($_GET['myvar']) ? $_GET['myvar'] : '';


if(!empty($deleteval)){

sql("update notification set status ='Closed' where `id`='$deleteval'", $eo);


}







   ?>



            <!--Main-->
            <main class="bg-white-300 flex-1 p-3 overflow-hidden">




       <div class="rounded overflow-hidden shadow bg-white mx-2 w-full">
                            <div class="px-6 py-2 border-b border-light-grey">

        <!-- partial -->
        <div class="main-panel" >
          <div class="content-wrapper">
            <div class="page-header">
          
            </div>
            <div class="row">




              <div class="col-lg-12 grid-margin stretch-card">
                <div class="card">
                  <div class="card-body">
                    <h4 class="card-title fontt"> التنبيهات</h4>
                   
                    <table class="table table-hover fontt tablev" dir="rtl">
                      <thead>
                        <tr>
                          <th scope="col">العنوان</th>
                          <th scope="col">الحاله</th>
                          <th scope="col">اسم المستخدم</th>
                          <th scope="col">الملاحظات</th>
                          <th scope="col"></th>
                        </tr>
                      </thead>
                      <tbody>



<?php

if($group == 2){


$us_id1 = sql("SELECT * FROM notification WHERE user1 = 'administrator' order by id desc", $eo);
$num_id1   = mysqli_num_rows($us_id1);
if ($num_id1 > 0){
while ( $rowid = mysqli_fetch_array($us_id1)) {
$product_id = $rowid['id'];


echo '

                        <tr dir="rtl">
                          <td  scope="row" data-label="العنوان">'.$rowid['subject'].'</td>
                          <td  scope="row" data-label="الحاله">'. $rowid['status'].'</td>
                          <td  scope="row" data-label="اسم المستخدم"><a class="btn" href="add_new_user.php?myvar='.$rowid['username'].'" > '. $rowid['username'].'</a></td>
                          <td  scope="row" data-label="الملاحظات ">'. $rowid['discribtion'].'</td>


                       <td><a class="btn" href="notification.php?myvar='.$product_id.'" style="color: red"> Close</a></td>


                        </tr>


';


}}

}

?>

                  
                         
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
     

           
            </div>
          </div>
          <!-- content-wrapper ends -->

          <!-- partial -->
        </div>
        <!-- main-panel ends -->
      </div>
</div></main></div></div></div></body></html>






















<script>
function myFunctionc2() {
  // Get the text field
  var copyText = document.getElementById("linkad");

  // Select the text field
  copyText.select();
  copyText.setSelectionRange(0, 99999); // For mobile devices

  // Copy the text inside the text field
  navigator.clipboard.writeText(copyText.value);
  
  // Alert the copied text
  alert("Copied the text: " + copyText.value);
}
</script>
<br><br><br>


              



<br><br><br><br>










                </div>
            </main>
            <!--/Main-->
        </div>


    </div>
<link href='https://fonts.googleapis.com/css?family=Cairo' rel='stylesheet'>
<style>

   .fontt{ font-family: 'Cairo';

text-align: right;

 }



</style>
</div>
<script src="main.js"></script>

    <script src="js/jquery.min.js"></script>
    <script src="js/popper.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>
</body>

</html>



