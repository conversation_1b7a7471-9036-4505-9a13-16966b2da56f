/* card */
.card {
	display: block;
	margin-bottom: 40px;
	.card-img {
		width: 100%;
		border-radius: $card-border-radius;
	}

	.card-body {
		padding: 10px;
		margin-top: 20px;
	}
	.card-subtitle {
		font-size: 13px;
		opacity: .8;
		font-weight: bold;
		margin-bottom: 3px;
	}
	.card-title {
		font-size: calc(17px + (22 - 17) * ((100vw - 300px) / (1300 - 300)));	
		margin-bottom: 15px;	
	}
}

/* img overlay */
.overlay-img {
	display: block;
	background: transparent;
	position: relative;
	overflow: hidden;
	margin-bottom: 25px;
	border-radius: $card-border-radius;

	img {
		width: 100%;
		border-radius: $card-border-radius;
	}

	.overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba($gray-800, .5);
		@include transition(all, 1s);
	}
	.des {
		position: absolute;
		top: 80%;
		left: 0;
		width: 100%;
		height: 100%;
		@include transition(all ,.8s);

		.title {
			font-weight: bold;
			font-size: 24px;
		}

		.subtitle {
			opacity: .7;
		}

		p {
			margin-top: 100px;
			opacity: 1;
			@include transition(all, 1.2s);
			opacity: 0;
			visibility: hidden;
		}

	}

	&:hover,
	&:focus {
		.overlay {
			background: rgba($gray-900, .8);				
		}
		.des {
			top: 40%;

			p {
				margin-top: 0;
				opacity: 1;
				visibility: visible;
			}
		}
	}
}